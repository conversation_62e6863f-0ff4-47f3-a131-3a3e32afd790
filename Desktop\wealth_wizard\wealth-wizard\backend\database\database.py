"""
Database connection and session management for the Ultimate Wealth Wizard
"""
import os
import json
from typing import Any, Optional
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from .models import Base, State, Strategy, Log, Execution

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./wealth_wizard.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)


def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class StateManager:
    """Helper class for managing system state"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a state value"""
        try:
            state_item = self.db.query(State).filter(State.key == key).first()
            if not state_item:
                return default
            
            # Parse value based on type
            if state_item.value_type == "json":
                return json.loads(state_item.value)
            elif state_item.value_type == "number":
                return float(state_item.value)
            elif state_item.value_type == "boolean":
                return state_item.value.lower() == "true"
            else:
                return state_item.value
        except Exception:
            return default
    
    def set(self, key: str, value: Any, description: str = None) -> bool:
        """Set a state value"""
        try:
            # Determine value type and serialize
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value)
                value_type = "json"
            elif isinstance(value, bool):
                value_str = str(value).lower()
                value_type = "boolean"
            elif isinstance(value, (int, float)):
                value_str = str(value)
                value_type = "number"
            else:
                value_str = str(value)
                value_type = "string"
            
            # Update or create state item
            state_item = self.db.query(State).filter(State.key == key).first()
            if state_item:
                state_item.value = value_str
                state_item.value_type = value_type
                if description:
                    state_item.description = description
            else:
                state_item = State(
                    key=key,
                    value=value_str,
                    value_type=value_type,
                    description=description
                )
                self.db.add(state_item)
            
            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False
    
    def increment(self, key: str, amount: float = 1.0) -> float:
        """Increment a numeric state value"""
        current = self.get(key, 0.0)
        new_value = float(current) + amount
        self.set(key, new_value)
        return new_value


def get_state_manager(db: Session) -> StateManager:
    """Get a state manager instance"""
    return StateManager(db)


def initialize_default_state(db: Session):
    """Initialize default system state values"""
    state_manager = StateManager(db)
    
    # Set default values if they don't exist
    defaults = {
        "current_money": 0.0,
        "total_earnings": 0.0,
        "system_status": "initialized",
        "loop_delay": 5.0,
        "strategies_enabled": True,
        "api_keys": {},
        "user_preferences": {},
        "system_uptime": 0
    }
    
    for key, value in defaults.items():
        if state_manager.get(key) is None:
            state_manager.set(key, value, f"Default {key} value")


def log_activity(db: Session, level: str, category: str, message: str, 
                strategy_name: str = None, earnings: float = None, metadata: dict = None):
    """Helper function to log system activity"""
    try:
        log_entry = Log(
            level=level,
            category=category,
            message=message,
            strategy_name=strategy_name,
            earnings=earnings,
            metadata=metadata
        )
        db.add(log_entry)
        db.commit()
    except Exception:
        db.rollback()
