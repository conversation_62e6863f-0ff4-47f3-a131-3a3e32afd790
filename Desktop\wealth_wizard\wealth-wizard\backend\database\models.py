"""
SQLAlchemy models for the Ultimate Wealth Wizard
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()


class Strategy(Base):
    """Model for storing wealth generation strategies"""
    __tablename__ = "strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=False)
    status = Column(String(20), default="active")  # active, inactive, paused
    success_rate = Column(Float, default=0.0)
    total_executions = Column(Integer, default=0)
    total_earnings = Column(Float, default=0.0)
    average_earnings = Column(Float, default=0.0)
    last_executed = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class Log(Base):
    """Model for storing system logs and activity"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=func.now(), index=True)
    level = Column(String(20), default="INFO")  # INFO, WARNING, ERROR, SUCCESS
    category = Column(String(50), nullable=False)  # STRATEGY, SYSTEM, USER_ACTION, etc.
    message = Column(Text, nullable=False)
    strategy_name = Column(String(100), nullable=True)
    earnings = Column(Float, nullable=True)
    metadata = Column(JSON, nullable=True)  # Additional structured data


class State(Base):
    """Model for storing system state as key-value pairs"""
    __tablename__ = "state"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(Text, nullable=False)  # JSON serialized values
    value_type = Column(String(20), default="string")  # string, number, boolean, json
    description = Column(Text, nullable=True)
    is_encrypted = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class Execution(Base):
    """Model for tracking individual strategy executions"""
    __tablename__ = "executions"
    
    id = Column(Integer, primary_key=True, index=True)
    strategy_name = Column(String(100), nullable=False, index=True)
    started_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(20), default="running")  # running, completed, failed, cancelled
    earnings = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    execution_data = Column(JSON, nullable=True)  # Strategy-specific execution details
    duration_seconds = Column(Float, nullable=True)
