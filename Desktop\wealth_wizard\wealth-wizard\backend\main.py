"""
Ultimate Wealth Wizard - FastAPI Backend
The autonomous wealth generation system's core engine
"""
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import uvicorn

from database.database import (
    create_tables, get_db, get_state_manager, initialize_default_state, 
    log_activity, SessionLocal
)
from database.models import Strategy, Log, State, Execution
from agents.content_agent import ContentAgent
from agents.market_agent import MarketAgent


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_text(json.dumps(message))
        except:
            self.disconnect(websocket)
    
    async def broadcast(self, message: dict):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                disconnected.append(connection)
        
        # Remove disconnected connections
        for conn in disconnected:
            self.disconnect(conn)


class WealthWizardCore:
    """Core autonomous wealth generation engine"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.is_running = False
        self.start_time = datetime.now()
        self.loop_count = 0
        
        # Initialize agents
        self.content_agent = ContentAgent()
        self.market_agent = MarketAgent()

        # Define available strategies
        self.strategies = [
            {
                "name": "AI Content Creation",
                "description": "Generate and monetize various forms of digital content using real AI",
                "agent": self.content_agent,
                "execute": self._execute_content_creation,
                "requirements": self.content_agent.get_requirements(),
                "enabled": True
            },
            {
                "name": "Market Analysis",
                "description": "Analyze market trends and identify hot topics using real news data",
                "agent": self.market_agent,
                "execute": self._execute_market_analysis,
                "requirements": self.market_agent.get_requirements(),
                "enabled": True
            },
            {
                "name": "Intelligent Workflow",
                "description": "Market analysis → Content creation workflow",
                "agent": None,
                "execute": self._execute_intelligent_workflow,
                "requirements": ["NewsAPI Key", "Google Gemini API Key"],
                "enabled": True
            },
            {
                "name": "Automated Trading",
                "description": "Execute low-risk trading strategies",
                "agent": None,
                "execute": self._simulate_trading,
                "requirements": ["Trading API", "Risk management tools"],
                "enabled": False  # Disabled for safety in simulation
            },
            {
                "name": "Affiliate Marketing",
                "description": "Promote products and earn commissions",
                "agent": None,
                "execute": self._simulate_affiliate_marketing,
                "requirements": ["Affiliate networks", "Marketing platforms"],
                "enabled": True
            }
        ]
    
    async def start_autonomous_loop(self):
        """Start the main autonomous wealth generation loop"""
        self.is_running = True
        
        # Initialize database
        with SessionLocal() as db:
            initialize_default_state(db)
            log_activity(db, "INFO", "SYSTEM", "Wealth Wizard autonomous loop started")
        
        await self._broadcast_status_update("System started - Beginning autonomous operations")
        
        while self.is_running:
            try:
                await self._execute_loop_cycle()
                
                # Get loop delay from state
                with SessionLocal() as db:
                    state_manager = get_state_manager(db)
                    delay = state_manager.get("loop_delay", 5.0)
                
                await asyncio.sleep(delay)
                
            except Exception as e:
                await self._broadcast_log("ERROR", "SYSTEM", f"Loop error: {str(e)}")
                await asyncio.sleep(10)  # Wait longer on error

    async def _execute_loop_cycle(self):
        """Execute one cycle of the autonomous loop"""
        self.loop_count += 1

        with SessionLocal() as db:
            state_manager = get_state_manager(db)

            # Check if strategies are enabled
            if not state_manager.get("strategies_enabled", True):
                await self._broadcast_log("INFO", "SYSTEM", "Strategies disabled - skipping cycle")
                return

            # Select a strategy
            enabled_strategies = [s for s in self.strategies if s["enabled"]]
            if not enabled_strategies:
                await self._broadcast_log("WARNING", "SYSTEM", "No enabled strategies available")
                return

            import random
            strategy = random.choice(enabled_strategies)

            await self._broadcast_log("INFO", "STRATEGY", f"Executing strategy: {strategy['name']}")

            # Check requirements
            missing_requirements = await self._check_requirements(strategy["requirements"])
            if missing_requirements:
                await self._broadcast_log(
                    "WARNING",
                    "USER_ACTION",
                    f"Missing requirements for {strategy['name']}: {', '.join(missing_requirements)}"
                )
                return

            # Execute strategy
            try:
                result = await strategy["execute"]()

                if result["success"]:
                    # Update money
                    new_total = state_manager.increment("current_money", result["gain"])
                    state_manager.increment("total_earnings", result["gain"])

                    await self._broadcast_log(
                        "SUCCESS",
                        "STRATEGY",
                        result["log"],
                        strategy["name"],
                        result["gain"]
                    )

                    await self._broadcast_money_update(new_total, result["gain"])

                    # Log execution
                    log_activity(
                        db, "SUCCESS", "STRATEGY", result["log"],
                        strategy["name"], result["gain"], result.get("metadata")
                    )
                else:
                    await self._broadcast_log("WARNING", "STRATEGY", result["log"], strategy["name"])
                    log_activity(db, "WARNING", "STRATEGY", result["log"], strategy["name"])

            except Exception as e:
                error_msg = f"Strategy {strategy['name']} failed: {str(e)}"
                await self._broadcast_log("ERROR", "STRATEGY", error_msg, strategy["name"])
                log_activity(db, "ERROR", "STRATEGY", error_msg, strategy["name"])

    async def _check_requirements(self, requirements: List[str]) -> List[str]:
        """Check if strategy requirements are met"""
        # In simulation mode, we'll simulate some missing requirements occasionally
        import random

        missing = []
        for req in requirements:
            # 10% chance of a requirement being "missing" for demonstration
            if random.random() < 0.1:
                missing.append(req)

        return missing

    # Real agent execution methods
    async def _execute_content_creation(self) -> Dict[str, Any]:
        """Execute real AI content creation"""
        with SessionLocal() as db:
            state_manager = get_state_manager(db)
            api_key = state_manager.get("gemini_api_key")

            return await self.content_agent.execute_ai_content_creation(api_key=api_key)

    async def _execute_market_analysis(self) -> Dict[str, Any]:
        """Execute real market analysis"""
        with SessionLocal() as db:
            state_manager = get_state_manager(db)
            api_key = state_manager.get("news_api_key")

            return await self.market_agent.analyze_market_trends(api_key=api_key)

    async def _execute_intelligent_workflow(self) -> Dict[str, Any]:
        """Execute the intelligent workflow: Market Analysis → Content Creation"""
        try:
            # Step 1: Analyze market trends
            await self._broadcast_log("INFO", "WORKFLOW", "🔍 Starting intelligent workflow: analyzing market trends...")

            market_result = await self._execute_market_analysis()

            if not market_result["success"]:
                return {
                    "success": False,
                    "gain": 0,
                    "log": f"Workflow failed at market analysis: {market_result['log']}",
                    "metadata": {"step": "market_analysis", "error": market_result.get("metadata", {})}
                }

            hot_topic = market_result.get("hot_topic")
            if not hot_topic:
                return {
                    "success": False,
                    "gain": 0,
                    "log": "Workflow failed: no hot topic identified",
                    "metadata": {"step": "topic_identification"}
                }

            # Log the identified trend
            await self._broadcast_log("INFO", "WORKFLOW", f"📈 Identified market trend: {hot_topic}")

            # Step 2: Create content about the hot topic
            await self._broadcast_log("INFO", "WORKFLOW", f"✍️ Creating AI content about: {hot_topic}")

            with SessionLocal() as db:
                state_manager = get_state_manager(db)
                api_key = state_manager.get("gemini_api_key")

                content_result = await self.content_agent.execute_ai_content_creation(
                    topic=hot_topic,
                    api_key=api_key
                )

            if content_result["success"]:
                total_gain = content_result["gain"] * 1.5  # Bonus for intelligent workflow
                return {
                    "success": True,
                    "gain": round(total_gain, 2),
                    "log": f"🧠 Intelligent workflow success: {hot_topic} → Content created - Earned ${total_gain:.2f}",
                    "metadata": {
                        "workflow": "market_to_content",
                        "hot_topic": hot_topic,
                        "content_result": content_result.get("metadata", {}),
                        "bonus_multiplier": 1.5
                    }
                }
            else:
                return {
                    "success": False,
                    "gain": 0,
                    "log": f"Workflow failed at content creation: {content_result['log']}",
                    "metadata": {"step": "content_creation", "hot_topic": hot_topic}
                }

        except Exception as e:
            return {
                "success": False,
                "gain": 0,
                "log": f"Intelligent workflow error: {str(e)}",
                "metadata": {"error": str(e)}
            }

    # Simulated strategy functions (legacy)
    async def _simulate_market_analysis(self) -> Dict[str, Any]:
        """Simulate market analysis strategy"""
        import random
        await asyncio.sleep(random.uniform(1, 2))

        if random.random() < 0.7:
            earnings = random.uniform(10, 45)
            return {
                "success": True,
                "gain": round(earnings, 2),
                "log": f"Market analysis identified profitable opportunity - Earned ${earnings:.2f}",
                "metadata": {"market": "crypto", "confidence": random.uniform(0.6, 0.9)}
            }
        else:
            return {
                "success": False,
                "gain": 0,
                "log": "Market analysis found no profitable opportunities",
                "metadata": {"reason": "market_volatility"}
            }

    async def _simulate_trading(self) -> Dict[str, Any]:
        """Simulate automated trading strategy"""
        import random
        await asyncio.sleep(random.uniform(2, 4))

        if random.random() < 0.6:
            earnings = random.uniform(20, 100)
            return {
                "success": True,
                "gain": round(earnings, 2),
                "log": f"Automated trade executed successfully - Earned ${earnings:.2f}",
                "metadata": {"trade_type": "swing", "duration": "4h"}
            }
        else:
            loss = random.uniform(5, 25)
            return {
                "success": False,
                "gain": -round(loss, 2),
                "log": f"Trade resulted in loss - Lost ${loss:.2f}",
                "metadata": {"reason": "market_reversal"}
            }

    async def _simulate_affiliate_marketing(self) -> Dict[str, Any]:
        """Simulate affiliate marketing strategy"""
        import random
        await asyncio.sleep(random.uniform(1, 3))

        products = ["Software Tool", "Online Course", "E-book", "Subscription Service"]
        product = random.choice(products)

        if random.random() < 0.65:
            earnings = random.uniform(8, 60)
            return {
                "success": True,
                "gain": round(earnings, 2),
                "log": f"Affiliate sale: {product} - Earned ${earnings:.2f} commission",
                "metadata": {"product": product, "commission_rate": random.uniform(0.1, 0.3)}
            }
        else:
            return {
                "success": False,
                "gain": 0,
                "log": f"No affiliate sales for {product} today",
                "metadata": {"product": product, "reason": "low_traffic"}
            }

    # Broadcast methods
    async def _broadcast_log(self, level: str, category: str, message: str,
                           strategy_name: str = None, earnings: float = None):
        """Broadcast log message to all connected clients"""
        log_data = {
            "type": "log",
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "category": category,
            "message": message,
            "strategy_name": strategy_name,
            "earnings": earnings
        }
        await self.connection_manager.broadcast(log_data)

    async def _broadcast_money_update(self, total: float, gain: float):
        """Broadcast money update to all connected clients"""
        money_data = {
            "type": "money_update",
            "total": total,
            "gain": gain,
            "timestamp": datetime.now().isoformat()
        }
        await self.connection_manager.broadcast(money_data)

    async def _broadcast_status_update(self, status: str):
        """Broadcast status update to all connected clients"""
        uptime = datetime.now() - self.start_time
        status_data = {
            "type": "status_update",
            "status": status,
            "uptime": str(uptime).split('.')[0],  # Remove microseconds
            "loop_count": self.loop_count,
            "timestamp": datetime.now().isoformat()
        }
        await self.connection_manager.broadcast(status_data)

    def stop(self):
        """Stop the autonomous loop"""
        self.is_running = False


# Global instances
manager = ConnectionManager()
wealth_wizard = WealthWizardCore(manager)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    create_tables()

    # Start the autonomous loop
    loop_task = asyncio.create_task(wealth_wizard.start_autonomous_loop())

    yield

    # Shutdown
    wealth_wizard.stop()
    loop_task.cancel()
    try:
        await loop_task
    except asyncio.CancelledError:
        pass


# FastAPI app
app = FastAPI(
    title="Ultimate Wealth Wizard",
    description="Autonomous wealth generation system",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Ultimate Wealth Wizard API", "status": "running"}


@app.get("/status")
async def get_status(db: Session = Depends(get_db)):
    """Get system status"""
    state_manager = get_state_manager(db)
    uptime = datetime.now() - wealth_wizard.start_time

    return {
        "status": "running" if wealth_wizard.is_running else "stopped",
        "uptime": str(uptime).split('.')[0],
        "loop_count": wealth_wizard.loop_count,
        "current_money": state_manager.get("current_money", 0.0),
        "total_earnings": state_manager.get("total_earnings", 0.0),
        "strategies_enabled": state_manager.get("strategies_enabled", True),
        "active_connections": len(manager.active_connections)
    }


@app.get("/logs")
async def get_logs(limit: int = 50, db: Session = Depends(get_db)):
    """Get recent logs"""
    logs = db.query(Log).order_by(Log.timestamp.desc()).limit(limit).all()
    return [
        {
            "id": log.id,
            "timestamp": log.timestamp.isoformat(),
            "level": log.level,
            "category": log.category,
            "message": log.message,
            "strategy_name": log.strategy_name,
            "earnings": log.earnings,
            "extra_data": log.extra_data
        }
        for log in logs
    ]


@app.post("/api-keys")
async def set_api_key(key_data: dict, db: Session = Depends(get_db)):
    """Set API keys for various services"""
    try:
        state_manager = get_state_manager(db)

        key_name = key_data.get("name")  # e.g., "gemini_api_key", "news_api_key"
        key_value = key_data.get("value")

        if not key_name or not key_value:
            return {"success": False, "message": "Missing key name or value"}

        # Store the API key
        success = state_manager.set(key_name, key_value, f"API key for {key_name}")

        if success:
            return {"success": True, "message": f"API key '{key_name}' set successfully"}
        else:
            return {"success": False, "message": "Failed to store API key"}

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}


@app.get("/api-keys/status")
async def get_api_key_status(db: Session = Depends(get_db)):
    """Get status of configured API keys"""
    try:
        state_manager = get_state_manager(db)

        keys_status = {
            "gemini_api_key": state_manager.get("gemini_api_key") is not None,
            "news_api_key": state_manager.get("news_api_key") is not None
        }

        return {"success": True, "keys": keys_status}

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket)

    # Send initial status
    with SessionLocal() as db:
        state_manager = get_state_manager(db)
        initial_data = {
            "type": "initial_state",
            "current_money": state_manager.get("current_money", 0.0),
            "total_earnings": state_manager.get("total_earnings", 0.0),
            "status": "connected",
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(initial_data, websocket)

    try:
        while True:
            # Keep connection alive and handle any incoming messages
            data = await websocket.receive_text()
            # Echo back for now (can be extended for commands)
            await manager.send_personal_message({"type": "echo", "data": data}, websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
