/* Additional styles for React components */

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin: 20px 0;
}

.status-item {
  text-align: center;
  padding: 10px;
  background: rgba(0,0,0,0.05);
  border-radius: 8px;
}

.status-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-value {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
}

.system-info {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.system-info h4 {
  margin-bottom: 10px;
  color: #555;
}

.strategy-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.strategy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0,0,0,0.03);
  border-radius: 6px;
  font-size: 0.9rem;
}

.strategy-name {
  font-weight: 500;
}

.strategy-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
}

.strategy-status.active {
  background-color: #d4edda;
  color: #155724;
}

.strategy-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.performance-metrics {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.performance-metrics h4 {
  margin-bottom: 10px;
  color: #555;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.metric span:first-child {
  color: #666;
}

.metric span:last-child {
  font-weight: bold;
  color: #333;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-weight: 500;
  color: #555;
}

.progress-values {
  font-size: 0.9rem;
  color: #666;
}

.progress-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  font-size: 0.85rem;
}

.progress-percentage {
  color: #666;
}

.progress-complete {
  color: #2ecc71;
  font-weight: bold;
}

.log-strategy {
  margin-top: 5px;
  padding-left: 10px;
}

.log-strategy small {
  color: #666;
  font-style: italic;
}

.log-stats {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  text-align: center;
  color: #666;
}

.money-animation {
  margin-top: 20px;
  text-align: center;
}

.money-particles {
  font-size: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* API Key Manager Styles */
.api-keys-status {
  margin: 15px 0;
}

.api-key-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(0,0,0,0.03);
  border-radius: 8px;
  border-left: 4px solid #ddd;
}

.api-key-item:has(.status-running) {
  border-left-color: #2ecc71;
}

.api-key-item:has(.status-stopped) {
  border-left-color: #e74c3c;
}

.api-key-info {
  flex: 1;
}

.api-key-name {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-key-description {
  font-size: 0.85rem;
  color: #666;
}

.api-key-status {
  font-size: 0.9rem;
  font-weight: bold;
}

.add-api-key-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-api-key-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.add-api-key-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.api-key-form {
  background: rgba(0,0,0,0.05);
  padding: 20px;
  border-radius: 8px;
  margin: 15px 0;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.form-actions button {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="submit"] {
  background: #2ecc71;
  color: white;
}

.form-actions button[type="submit"]:hover:not(:disabled) {
  background: #27ae60;
}

.form-actions button[type="submit"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-actions button[type="button"] {
  background: #95a5a6;
  color: white;
}

.form-actions button[type="button"]:hover {
  background: #7f8c8d;
}

.message {
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.api-key-help {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.api-key-help h4 {
  margin-bottom: 10px;
  color: #555;
}

.api-key-help ul {
  margin: 0;
  padding-left: 20px;
}

.api-key-help li {
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.api-key-help a {
  color: #667eea;
  text-decoration: none;
}

.api-key-help a:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }

  .earnings-info {
    flex-direction: column;
    gap: 10px;
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .api-key-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-actions {
    flex-direction: column;
  }
}
