@echo off
echo ========================================
echo   🧙‍♂️ Ultimate Wealth Wizard - Backend
echo ========================================
echo.

cd backend

echo 📦 Installing Python dependencies...
py -m pip install -r requirements.txt
if errorlevel 1 (
    echo Trying alternative Python command...
    python -m pip install -r requirements.txt
)

echo.
echo 🚀 Starting FastAPI server...
echo.
echo 🌐 Backend will be available at: http://localhost:8000
echo 🔌 WebSocket endpoint: ws://localhost:8000/ws
echo 🔑 API Management: http://localhost:8000/api-keys/status
echo.
echo ⚡ The autonomous loop will start immediately!
echo 💰 Watch your wealth grow in real-time!
echo.

py main.py
if errorlevel 1 (
    echo Trying alternative Python command...
    python main.py
)

pause
