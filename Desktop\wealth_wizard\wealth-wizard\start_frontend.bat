@echo off
echo ========================================
echo   🧙‍♂️ Ultimate Wealth Wizard - Frontend
echo ========================================
echo.

cd frontend

REM Check if dependencies are installed
if not exist "node_modules\" (
    echo 📦 Installing Node.js dependencies...
    echo ⚠️  This may take a few minutes on first run...
    npm install
    if errorlevel 1 (
        echo ❌ npm install failed - check disk space and try again
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependencies already installed, skipping npm install
)

echo.
echo 🚀 Starting React development server...
echo.
echo 🌐 Frontend will be available at: http://localhost:3000
echo 📊 Real-time dashboard with live updates
echo 🔑 API key management included
echo.
echo 🎯 Make sure the backend is running first!
echo.

npm start

pause
